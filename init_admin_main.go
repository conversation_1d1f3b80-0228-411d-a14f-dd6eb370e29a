package main

import (
	"fmt"
	"log"
	"os"
	"solve-api/config"
	"solve-api/database"
	"solve-api/models"
	"solve-api/utils"
)

func main() {
	// 加载环境变量文件
	if _, err := os.Stat(".env"); err == nil {
		log.Println("发现 .env 文件，正在加载...")
		if err := utils.LoadEnvFile(".env"); err != nil {
			log.Printf("警告: 加载 .env 文件失败: %v", err)
		}
	} else {
		log.Println("未发现 .env 文件，使用系统环境变量")
	}

	// 加载配置
	config.LoadConfig()

	// 初始化数据库
	database.InitDatabase()

	// 检查是否已存在管理员用户
	var count int64
	database.DB.Model(&models.AdminUser{}).Where("username = ?", "admin").Count(&count)
	
	if count > 0 {
		fmt.Println("管理员用户 'admin' 已存在")
		return
	}

	// 对密码进行哈希
	hashedPassword, err := utils.HashPassword("admin")
	if err != nil {
		log.Fatalf("密码哈希失败: %v", err)
	}

	// 创建管理员用户
	admin := models.AdminUser{
		Username: "admin",
		Password: hashedPassword,
		Role:     "super_admin",
		Status:   "active",
	}

	if err := database.DB.Create(&admin).Error; err != nil {
		log.Fatalf("创建管理员用户失败: %v", err)
	}

	fmt.Printf("管理员用户创建成功!\n")
	fmt.Printf("用户名: admin\n")
	fmt.Printf("密码: admin\n")
	fmt.Printf("角色: super_admin\n")
	fmt.Printf("状态: active\n")
	fmt.Printf("\n登录接口:\n")
	fmt.Printf("POST http://localhost:8080/admin/login\n")
	fmt.Printf("{\n")
	fmt.Printf("  \"username\": \"admin\",\n")
	fmt.Printf("  \"password\": \"admin\"\n")
	fmt.Printf("}\n")
}
