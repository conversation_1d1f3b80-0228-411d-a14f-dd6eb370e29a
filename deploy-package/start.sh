#!/bin/bash
# 启动脚本

# 设置工作目录
cd /www/wwwroot/solve-api

# 加载环境变量
source ./env.sh

# 创建日志目录
mkdir -p logs

# 启动服务
echo "启动 Solve API 服务..."
echo "时间: $(date)"
echo "工作目录: $(pwd)"
echo "服务地址: ${SERVER_HOST}:${SERVER_PORT}"

# 后台运行并记录日志
nohup ./solve-api > logs/app.log 2>&1 &

# 获取进程ID
PID=$!
echo $PID > solve-api.pid

echo "服务已启动，PID: $PID"
echo "日志文件: logs/app.log"
echo "PID文件: solve-api.pid"

# 等待几秒检查服务状态
sleep 3

if ps -p $PID > /dev/null; then
    echo "✅ 服务启动成功"
    echo "🌐 访问地址: http://$(hostname -I | awk '{print $1}'):${SERVER_PORT}/health"
else
    echo "❌ 服务启动失败，请检查日志"
    cat logs/app.log
fi
