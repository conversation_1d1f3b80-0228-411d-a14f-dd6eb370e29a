# Redis 持久化配置文件
# 用于确保缓存数据的持久化存储

# 基本配置
port 6379
bind 0.0.0.0
protected-mode yes
requirepass 4HY8xm8dECYmDSeaX8GC

# 持久化配置
# RDB 持久化 - 定期快照
save 900 1      # 900秒内至少1个key变化时保存
save 300 10     # 300秒内至少10个key变化时保存  
save 60 10000   # 60秒内至少10000个key变化时保存

# RDB 文件配置
dbfilename dump.rdb
dir /var/lib/redis/
rdbcompression yes
rdbchecksum yes

# AOF 持久化 - 追加文件
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec  # 每秒同步一次

# AOF 重写配置
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 内存管理
maxmemory 1gb
maxmemory-policy allkeys-lru

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log

# 网络配置
timeout 300
tcp-keepalive 300

# 安全配置
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""

# 性能优化
tcp-backlog 511
databases 16
