# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=solve

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=release

# API密钥配置
QWEN_API_KEY=your_qwen_api_key
QWEN_API_URL=https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
DEEPSEEK_API_KEY=your_deepseek_api_key

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# JWT配置
JWT_SECRET=your_jwt_secret_key

# 限流配置
API_RATE_LIMIT=60

# 缓存配置 (0表示永不过期)
CACHE_TTL=0
