使用一个 映射表 + 字符遍历替换的方式来实现，确保不会乱码，并保持可控性。一劳永逸的方法解决标点符号转码以及防止乱码的问题



package main

import (
	"fmt"
	"strings"
)

var punctuationMap = map[rune]rune{
	'，': ',', '。': '.', '！': '!', '？': '?',
	'：': ':', '；': ';', '“': '"', '”': '"',
	'‘': '\'', '’': '\'', '（': '(', '）': ')',
	'【': '[', '】': ']', '《': '<', '》': '>',
	'、': ',', '～': '~', '—': '-', '　': ' ', // 全角空格
}

func NormalizePunctuation(text string) string {
	var builder strings.Builder
	for _, ch := range text {
		if mapped, ok := punctuationMap[ch]; ok {
			builder.WriteRune(mapped)
		} else {
			builder.WriteRune(ch)
		}
	}
	return builder.String()
}

func main() {
	input := `“你好，世界！” 你（好吗）？　今天是：2025年6月4日。`
	output := NormalizePunctuation(input)
	fmt.Println(output)
}