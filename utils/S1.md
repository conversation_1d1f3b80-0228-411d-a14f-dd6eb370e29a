Qwen的prompt



https://www.bonuspoints.uzdns.com/20250604132258f988d2353.jpg
https://www.bonuspoints.uzdns.com/20250604132329967855981.jpg
https://www.bonuspoints.uzdns.com/20250604132436c60ed6660.jpg



prompt := fmt.Sprintf(`你是一个专业的题目分析专家，请分析以下题目并给出正确答案。

题目信息：
%s

【重要要求】：
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
3. 必须给出明确的答案选项

【输出格式】：
答案：[选择正确的选项，如A、B、C、D或Y、N等，要把选项文字带上，多选题要实现答案选项换行]
解析：[要完整的解析，但是不要说来源]`, questionData)



帮我生成几个log到根目录下的log文件
1.qwen返回的原始数据
2.我们将qwen原始数据解析后的json 
3.将解析后的json提交deepseek后deepseek返回的原始数据
4.我们将deepseek返回的数据进行解析后的json
每次运行的时候都给我生成这几个文件




prompt := fmt.Sprintf(`你是一个专业的题目分析专家，请分析以下题目并给出正确答案。

题目信息：
%s

【重要要求】：
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
3. 必须给出明确的答案选项

【强制要求】：
输出的内容标点符号必须是半角符号；
输出的格式必须是按照题型


【输出约定】
1. 判断题答案输出约定按照以下格式处理
答案：[Y：正确]或者[N：错误]
解析：[完整的解析]

2. 单选题答案输出约定按照以下格式处理
答案：[A：真实答案内容]或者[B：真实答案内容]
解析：[完整的解析]

3. 多选题答案输出约定按照以下格式处理
答案：[A：真实答案内容；B：真实答案内容；C：真实答案内容；D：真实答案内容]
解析：[完整的解析]
    
`, questionData)


现将deepseek返回的原始数据进行换行符与空格进行过滤，然后将标点符号与冒号等转换为半角符号。谨慎处理，不要在出现过滤掉了正常文字或者出现乱码的情况
针对不同题型修改json解析格式，按下面格式处理

1. 判断题答案输出约定按照以下格式处理
答案：[Y：正确]或者[N：错误]
解析：[完整的解析]

2. 单选题答案输出约定按照以下格式处理
答案：[A：真实答案内容]或者[B：真实答案内容]
解析：[完整的解析]

3. 多选题答案输出约定按照以下格式处理
答案：[A：真实答案内容；B：真实答案内容；C：真实答案内容；D：真实答案内容]
解析：[完整的解析]















{
  "id": "0475225a-8520-4c43-9fc5-e9d9df0ec733",
  "object": "chat.completion",
  "created": 1749035129,
  "model": "deepseek-chat",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "答案：超过额定乘员20% 的 , 处500元以上2000 元以下罚款 运输单位的车辆有上述情形 ，经处罚不改的 对 直接负责的主管人员处 2000 元以 上 5000 元 下罚款 由公安机关交通管理部门扣留机动车至违法状态消除 超 额定乘客未达 % 的 。 处 200 元上 于 .下罚款\n解析：根据《中华人民共和国道路交通安全法》第九十二条规定，公路客运车辆载客超过额定乘员的，由公安机关交通管理部门扣留机动车至违法状态消除；超过额定乘员20%的，处500元以上2000元以下罚款；超过额定乘员未达20%的，处200元以上500元以下罚款。运输单位的车辆有上述情形，经处罚不改的，对直接负责的主管人员处2000元以上5000元以下罚款。因此，所有选项均符合法律规定。"
      }
    }
  ]
}