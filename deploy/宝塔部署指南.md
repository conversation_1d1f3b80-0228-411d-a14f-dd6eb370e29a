# 拍照搜题API v2.0 - 宝塔面板部署指南

## 🎉 版本特性

### ✨ v2.0 新功能
- 🏗️ **MySQL + Redis 混合缓存** - 双层缓存架构，数据永不丢失
- 🔄 **智能故障恢复** - Redis故障时自动降级到MySQL，被动恢复机制
- 📊 **完整缓存统计** - 缓存命中率、使用统计、性能监控
- 🛡️ **生产级安全** - 完整的安全防护和速率限制
- 🌍 **环境变量支持** - 灵活的配置管理

## 📋 部署步骤

### 1. 准备工作

#### 1.1 确认服务器架构
```bash
uname -m
# x86_64 -> 使用 solve-api-v2.0-linux-amd64
# aarch64 -> 使用 solve-api-v2.0-linux-arm64
```

#### 1.2 创建部署目录
```bash
mkdir -p /www/wwwroot/solve-api
cd /www/wwwroot/solve-api
```

### 2. 文件上传

#### 2.1 上传文件
- 上传对应架构的可执行文件到 `/www/wwwroot/solve-api/`
- 重命名为 `solve-api`
- 上传所有脚本文件 (env.sh, start.sh, stop.sh, restart.sh, status.sh)

#### 2.2 设置权限
```bash
chmod +x solve-api
chmod +x *.sh
```

### 3. 配置环境

#### 3.1 修改环境变量
编辑 `env.sh` 文件，设置正确的配置：
- 数据库密码
- Redis密码  
- API密钥
- 其他配置

#### 3.2 确保数据库和Redis可用
- MySQL 数据库已创建
- Redis 服务正常运行
- 网络连接正常

### 4. 启动服务

#### 4.1 启动
```bash
./start.sh
```

#### 4.2 检查状态
```bash
./status.sh
```

#### 4.3 查看日志
```bash
tail -f logs/app.log
```

### 5. 宝塔面板配置

#### 5.1 添加网站
- 在宝塔面板添加网站
- 域名：你的域名
- 根目录：/www/wwwroot/solve-api

#### 5.2 配置反向代理
在网站设置 -> 反向代理中添加：
- 代理名称：solve-api
- 目标URL：http://127.0.0.1:8080
- 发送域名：$host
- 内容替换：关闭

#### 5.3 配置SSL（推荐）
- 申请SSL证书
- 开启强制HTTPS

### 6. 防火墙配置

#### 6.1 开放端口
在宝塔面板 -> 安全 中开放：
- 8080 端口（API服务）
- 3306 端口（MySQL，如果需要外部访问）
- 6379 端口（Redis，如果需要外部访问）

### 7. 进程管理

#### 7.1 使用脚本管理
```bash
./start.sh    # 启动
./stop.sh     # 停止  
./restart.sh  # 重启
./status.sh   # 状态检查
```

#### 7.2 设置开机自启
在 `/etc/rc.local` 中添加：
```bash
cd /www/wwwroot/solve-api && ./start.sh
```

### 8. 监控和维护

#### 8.1 日志监控
```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep -i error logs/app.log
```

#### 8.2 性能监控
```bash
# 查看进程资源使用
top -p $(cat solve-api.pid)

# 查看网络连接
netstat -tlnp | grep 8080
```

### 9. 常见问题

#### 9.1 服务无法启动
- 检查端口是否被占用
- 检查数据库连接
- 检查Redis连接
- 查看错误日志

#### 9.2 API调用失败
- 检查API密钥配置
- 检查网络连接
- 检查防火墙设置

#### 9.3 性能问题
- 检查数据库连接池
- 检查Redis连接
- 监控系统资源使用

## 🔧 维护命令

```bash
# 查看服务状态
./status.sh

# 重启服务
./restart.sh

# 查看实时日志
tail -f logs/app.log

# 检查API健康
curl http://localhost:8080/health

# 查看进程
ps aux | grep solve-api
```
