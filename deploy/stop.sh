#!/bin/bash
# 停止脚本

cd /www/wwwroot/solve-api

if [ -f solve-api.pid ]; then
    PID=$(cat solve-api.pid)
    echo "停止服务，PID: $PID"
    
    if ps -p $PID > /dev/null; then
        kill $PID
        echo "正在停止服务..."
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null; then
                echo "✅ 服务已停止"
                rm -f solve-api.pid
                exit 0
            fi
            sleep 1
        done
        
        # 强制停止
        echo "强制停止服务..."
        kill -9 $PID
        rm -f solve-api.pid
        echo "✅ 服务已强制停止"
    else
        echo "⚠️ 进程不存在，清理PID文件"
        rm -f solve-api.pid
    fi
else
    echo "⚠️ 未找到PID文件，尝试查找进程..."
    PID=$(pgrep -f solve-api)
    if [ ! -z "$PID" ]; then
        echo "找到进程 PID: $PID，正在停止..."
        kill $PID
        echo "✅ 服务已停止"
    else
        echo "ℹ️ 未找到运行中的服务"
    fi
fi
