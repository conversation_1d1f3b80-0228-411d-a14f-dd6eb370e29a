#!/bin/bash
# 状态检查脚本

cd /www/wwwroot/solve-api

echo "📊 Solve API 服务状态检查"
echo "================================"

# 检查PID文件
if [ -f solve-api.pid ]; then
    PID=$(cat solve-api.pid)
    echo "PID文件: solve-api.pid (PID: $PID)"
    
    if ps -p $PID > /dev/null; then
        echo "✅ 服务状态: 运行中"
        echo "📈 进程信息:"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem
    else
        echo "❌ 服务状态: 已停止 (PID文件存在但进程不存在)"
    fi
else
    echo "⚠️ PID文件: 不存在"
    
    # 查找可能的进程
    PID=$(pgrep -f solve-api)
    if [ ! -z "$PID" ]; then
        echo "⚠️ 发现运行中的进程 (PID: $PID)"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem
    else
        echo "❌ 服务状态: 未运行"
    fi
fi

echo ""
echo "🌐 服务检查:"

# 检查端口
PORT=${SERVER_PORT:-8080}
if netstat -tlnp 2>/dev/null | grep ":$PORT " > /dev/null; then
    echo "✅ 端口 $PORT: 已监听"
else
    echo "❌ 端口 $PORT: 未监听"
fi

# 检查健康接口
echo ""
echo "🏥 健康检查:"
if command -v curl > /dev/null; then
    RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:$PORT/health 2>/dev/null)
    if [ "$RESPONSE" = "200" ]; then
        echo "✅ 健康检查: 通过 (HTTP 200)"
    else
        echo "❌ 健康检查: 失败 (HTTP $RESPONSE)"
    fi
else
    echo "⚠️ 健康检查: curl 未安装，无法检查"
fi

# 显示最近日志
echo ""
echo "📝 最近日志 (最后10行):"
if [ -f logs/app.log ]; then
    tail -10 logs/app.log
else
    echo "⚠️ 日志文件不存在"
fi
