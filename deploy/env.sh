#!/bin/bash
# 生产环境变量配置文件
# 请根据实际情况修改以下配置

# 数据库配置
export DB_HOST="***********"
export DB_PORT="3380"
export DB_USER="gmdns"
export DB_PASSWORD="5e7fFn3HpPfuQ6Qx42Az"
export DB_NAME="solve"

# Redis配置
export REDIS_HOST="************"
export REDIS_PORT="6379"
export REDIS_PASSWORD="4HY8xm8dECYmDSeaX8GC"
export REDIS_DB="0"

# 服务器配置
export SERVER_HOST="0.0.0.0"
export SERVER_PORT="8080"
export GIN_MODE="release"

# 阿里云 Qwen-VL API 配置
export QWEN_API_KEY="sk-3920274bedf642c2b7495f534aadca84"
export QWEN_API_URL="https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"

# DeepSeek API 配置
export DEEPSEEK_API_KEY="***********************************"

# 日志配置
export LOG_LEVEL="info"
export LOG_FILE="logs/app.log"

# 安全配置
export JWT_SECRET="your-production-jwt-secret-key-here"
export API_RATE_LIMIT="60"

# 缓存配置
export CACHE_TTL="0"
