package main

import (
	"log"
	"os"
	"solve-api/config"
	"solve-api/database"
	"solve-api/handlers"
	"solve-api/middleware"
	"solve-api/utils"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载环境变量文件
	if _, err := os.Stat(".env"); err == nil {
		log.Println("发现 .env 文件，正在加载...")
		if err := utils.LoadEnvFile(".env"); err != nil {
			log.Printf("警告: 加载 .env 文件失败: %v", err)
		}
	} else {
		log.Println("未发现 .env 文件，使用系统环境变量")
	}

	// 打印环境变量状态
	utils.PrintEnvStatus()

	// 加载配置
	config.LoadConfig()

	// 初始化数据库
	database.InitDatabase()

	// 创建 Gin 引擎
	r := gin.Default()

	// 添加安全中间件
	r.Use(func(c *gin.Context) {
		// CORS 配置 - 生产环境应该限制具体域名
		origin := c.GetHeader("Origin")
		if origin == "" {
			origin = "*" // 开发环境允许所有域名
		}
		c.Header("Access-Control-Allow-Origin", origin)
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		c.Header("Access-Control-Allow-Credentials", "true")

		// 安全头
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 创建速率限制器 (每分钟60次请求)
	rateLimiter := middleware.NewRateLimiter(60, 60)

	// 创建处理器
	solveHandler := handlers.NewSolveHandler()
	adminHandler := handlers.NewAdminHandler()

	// API 路由组（需要 API Key 认证）
	api := r.Group("/api")
	api.Use(middleware.RateLimitMiddleware(rateLimiter))
	api.Use(middleware.AuthMiddleware())
	api.Use(middleware.LoggingMiddleware())
	{
		api.POST("/solve", solveHandler.Solve)
	}

	// 管理后台路由组
	admin := r.Group("/admin")
	{
		// 登录接口（不需要认证）
		admin.POST("/login", adminHandler.Login)

		// 需要管理员认证的接口
		adminAuth := admin.Group("")
		adminAuth.Use(middleware.AdminAuthMiddleware())
		{
			// API Key 管理
			adminAuth.POST("/api-keys", adminHandler.CreateAPIKey)
			adminAuth.GET("/api-keys", adminHandler.ListAPIKeys)
			adminAuth.PUT("/api-keys/:id/status", adminHandler.UpdateAPIKeyStatus)

			// 调用日志查看
			adminAuth.GET("/call-logs", adminHandler.ListAPICallLogs)

			// 缓存管理
			adminAuth.GET("/cache/stats", adminHandler.GetCacheStats)
			adminAuth.DELETE("/cache", adminHandler.ClearCache)
		}
	}

	// 健康检查接口（不需要认证）
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "服务运行正常",
		})
	})

	// 启动服务器
	serverAddr := config.AppConfig.Server.Host + ":" + config.AppConfig.Server.Port
	log.Printf("服务器启动在: %s", serverAddr)
	
	if err := r.Run(serverAddr); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
