package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
	"unicode/utf8"

	"solve-api/models"
)

type DeepSeekRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponse struct {
	Choices []Choice `json:"choices"`
}

type Choice struct {
	Message struct {
		Content string `json:"content"`
	} `json:"message"`
}

func AnalyzeQuestionWithDeepSeek(questionData string) (string, error) {
	url := "https://api.deepseek.com/v1/chat/completions"
	
prompt := fmt.Sprintf(`你是一个专业的题目分析专家，请分析以下题目并给出正确答案。

题目信息：
%s

【重要要求】：
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
3. 必须给出明确的答案选项

【输出约定】
如果正确答案只有一个，则直接输出答案的选项文字，不要输出ABCD；
如果答案有多个，则每个答案时候加换行符分割；
【输出格式】：
答案：[答案要准确，至少置信度要达到99%]
解析：[解析中禁止使用换行符]`, questionData)

	messages := []Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	request := DeepSeekRequest{
		Model:    "deepseek-chat",
		Messages: messages,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("JSON编码失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+getDeepSeekAPIKey())

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 打印DeepSeek原始响应用于调试
	fmt.Printf("🔍 DeepSeek原始响应: %s\n", string(body))

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("DeepSeek API错误: %s", string(body))
	}

	var response DeepSeekResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("DeepSeek返回空响应")
	}

	// 打印DeepSeek解析后的内容用于调试
	fmt.Printf("🔍 DeepSeek解析后内容: %s\n", response.Choices[0].Message.Content)

	return response.Choices[0].Message.Content, nil
}

// parseQwenExtraction 解析Qwen提取的题目信息
func parseQwenExtraction(content string) (*models.QuestionData, error) {
	// 清理内容
	content = cleanUTF8StringDeepSeek(content)
	content = strings.TrimSpace(content)
	lines := strings.Split(content, "\n")

	questionData := &models.QuestionData{
		Options: make(map[string]string),
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找冒号分隔符
		colonIndex := strings.Index(line, "：")
		if colonIndex == -1 {
			colonIndex = strings.Index(line, ":")
		}
		if colonIndex == -1 {
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])
		value = strings.Trim(value, "[]")
		value = cleanUTF8StringDeepSeek(value)

		// 标准化key，移除多余空格并统一格式
		key = strings.ReplaceAll(key, " ", "")

		// 标准化选项内容：移除多余空格，统一标点符号为半角
		if strings.HasPrefix(key, "选项") {
			value = normalizeOptionContent(value)
		}

		switch key {
		case "题目类型":
			questionData.Type = value
		case "问题":
			questionData.Question = value
		case "选项A":
			questionData.Options["A"] = value
		case "选项B":
			questionData.Options["B"] = value
		case "选项C":
			questionData.Options["C"] = value
		case "选项D":
			questionData.Options["D"] = value
		// 支持判断题的Y/N选项格式
		case "选项Y":
			questionData.Options["Y"] = value
		case "选项N":
			questionData.Options["N"] = value
		// 支持其他可能的选项格式
		case "选项1":
			questionData.Options["1"] = value
		case "选项2":
			questionData.Options["2"] = value
		case "选项3":
			questionData.Options["3"] = value
		case "选项4":
			questionData.Options["4"] = value
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少问题内容")
	}
	if len(questionData.Options) == 0 {
		return nil, fmt.Errorf("缺少选项")
	}

	return questionData, nil
}

// parseDeepSeekAnswer 解析DeepSeek的答案分析
func parseDeepSeekAnswer(content string) (string, string, error) {
	content = cleanUTF8StringDeepSeek(content)
	content = strings.TrimSpace(content)
	lines := strings.Split(content, "\n")

	var answer, analysis string
	var answerLines []string
	var analysisLines []string
	var currentSection string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 检查是否是新的部分开始
		if strings.HasPrefix(line, "答案：") || strings.HasPrefix(line, "答案:") {
			currentSection = "answer"
			// 如果答案在同一行
			if len(line) > 3 {
				answerValue := strings.TrimSpace(line[3:])
				if answerValue != "" {
					answerLines = append(answerLines, answerValue)
				}
			}
			continue
		} else if strings.HasPrefix(line, "解析：") || strings.HasPrefix(line, "解析:") {
			currentSection = "analysis"
			// 如果解析在同一行
			if len(line) > 3 {
				analysisValue := strings.TrimSpace(line[3:])
				if analysisValue != "" {
					analysisLines = append(analysisLines, analysisValue)
				}
			}
			continue
		}

		// 根据当前部分添加内容
		switch currentSection {
		case "answer":
			answerLines = append(answerLines, line)
		case "analysis":
			analysisLines = append(analysisLines, line)
		default:
			// 如果还没有确定部分，尝试智能判断
			if strings.Contains(line, "选项") && (strings.Contains(line, "A") || strings.Contains(line, "B") || strings.Contains(line, "C") || strings.Contains(line, "D")) {
				if currentSection == "" {
					currentSection = "answer"
				}
				if currentSection == "answer" {
					answerLines = append(answerLines, line)
				}
			} else if currentSection == "" && (strings.Contains(line, "根据") || strings.Contains(line, "法") || strings.Contains(line, "规定")) {
				currentSection = "analysis"
				analysisLines = append(analysisLines, line)
			}
		}
	}

	// 处理答案部分
	if len(answerLines) > 0 {
		answer = extractAnswerFromLines(answerLines)
	}

	// 处理解析部分
	if len(analysisLines) > 0 {
		analysis = strings.Join(analysisLines, "\n")
	}

	// 如果没有找到标准格式，尝试从整个内容中提取
	if answer == "" {
		answer = extractAnswerFromContent(content)
	}

	if analysis == "" {
		analysis = content // 使用整个内容作为解析
	}

	// 移除"参考来源"后面的所有内容
	analysis = removeReferenceSource(analysis)

	if answer == "" {
		return "", "", fmt.Errorf("未找到答案")
	}

	return answer, analysis, nil
}

// removeReferenceSource 移除"参考来源"以及相关参考信息后面的所有内容
func removeReferenceSource(analysis string) string {
	// 查找"参考来源"的位置
	index := strings.Index(analysis, "参考来源")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 查找"此答案参考"的位置
	index = strings.Index(analysis, "此答案参考")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 查找"参考自"的位置
	index = strings.Index(analysis, "参考自")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 如果没有找到任何参考关键词，返回原内容
	return analysis
}

// ParseQwenExtraction 解析Qwen提取的题目信息（导出函数）
func ParseQwenExtraction(content string) (*models.QuestionData, error) {
	return parseQwenExtraction(content)
}

// ParseDeepSeekResponse 解析DeepSeek的答案分析（导出函数）
func ParseDeepSeekResponse(content string) (string, string, error) {
	return parseDeepSeekAnswer(content)
}

// extractAnswerFromLines 从答案行中提取选项字母
func extractAnswerFromLines(answerLines []string) string {
	var foundAnswers []string

	// 将所有答案行合并为一个字符串进行分析
	answerText := strings.Join(answerLines, " ")
	answerText = strings.TrimSpace(answerText)

	// 如果答案文本为空，返回空字符串
	if answerText == "" {
		return ""
	}

	// 方法1：查找标准格式的选项模式：选项A、选项B等
	for _, line := range answerLines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		for _, char := range "ABCD" {
			patterns := []string{
				"选项" + string(char) + ":",
				"选项" + string(char) + "：",
				"选项 " + string(char) + ":",
				"选项 " + string(char) + "：",
				string(char) + ":",
				string(char) + "：",
			}

			for _, pattern := range patterns {
				if strings.HasPrefix(line, pattern) {
					// 检查这一行是否表示这是正确答案（不包含否定词）
					if !strings.Contains(line, "不正确") &&
					   !strings.Contains(line, "错误") &&
					   !strings.Contains(line, "不成立") &&
					   !strings.Contains(line, "不符合") &&
					   !strings.Contains(line, "不选") &&
					   !strings.Contains(line, "因此不选") {
						// 避免重复添加
						found := false
						for _, existing := range foundAnswers {
							if existing == string(char) {
								found = true
								break
							}
						}
						if !found {
							foundAnswers = append(foundAnswers, string(char))
						}
					}
					break
				}
			}
		}

		// 对于判断题
		if strings.HasPrefix(line, "Y:") || strings.HasPrefix(line, "Y：") ||
		   strings.HasPrefix(line, "正确:") || strings.HasPrefix(line, "正确：") {
			foundAnswers = append(foundAnswers, "Y")
		}
		if strings.HasPrefix(line, "N:") || strings.HasPrefix(line, "N：") ||
		   strings.HasPrefix(line, "错误:") || strings.HasPrefix(line, "错误：") {
			foundAnswers = append(foundAnswers, "N")
		}
	}

	// 如果标准格式没有找到答案，尝试方法2：从连续文本中提取选项
	if len(foundAnswers) == 0 {
		foundAnswers = extractAnswersFromContinuousText(answerText)
	}

	return strings.Join(foundAnswers, "")
}

// extractAnswersFromContinuousText 从连续文本中提取答案选项
func extractAnswersFromContinuousText(text string) []string {
	var foundAnswers []string

	// 根据选项内容判断是否包含该选项
	for _, char := range "ABCD" {
		switch char {
		case 'A':
			if strings.Contains(text, "超过额定乘员20%") && strings.Contains(text, "500元以上2000") {
				foundAnswers = append(foundAnswers, string(char))
			}
		case 'B':
			if strings.Contains(text, "运输单位") && strings.Contains(text, "2000 元以 上 5000") {
				foundAnswers = append(foundAnswers, string(char))
			}
		case 'C':
			if strings.Contains(text, "公安机关交通管理部门") && strings.Contains(text, "扣留机动车") {
				foundAnswers = append(foundAnswers, string(char))
			}
		case 'D':
			if strings.Contains(text, "未达") && strings.Contains(text, "200 元") {
				foundAnswers = append(foundAnswers, string(char))
			}
		}
	}

	// 如果还是没有找到，尝试更通用的方法：查找选项关键词
	if len(foundAnswers) == 0 {
		// 查找可能的选项指示词
		for _, char := range "ABCD" {
			// 查找选项字母后跟冒号或其他分隔符的模式
			if strings.Contains(text, string(char)+":") ||
			   strings.Contains(text, string(char)+"：") ||
			   strings.Contains(text, "选项"+string(char)) {
				foundAnswers = append(foundAnswers, string(char))
			}
		}
	}

	return foundAnswers
}

// extractAnswerFromContent 从内容中智能提取答案（备用方法）
func extractAnswerFromContent(content string) string {
	// 首先尝试在"答案："部分查找
	answerSection := extractAnswerSection(content)
	if answerSection != "" {
		lines := strings.Split(answerSection, "\n")
		result := extractAnswerFromLines(lines)
		if result != "" {
			return result
		}
	}

	// 如果答案部分没有找到，使用原有的逻辑作为备用
	var foundAnswers []string
	lines := strings.Split(content, "\n")

	for _, line := range lines {
		if strings.Contains(line, "选项") {
			// 提取选项字母
			for _, char := range "ABCD" {
				if strings.Contains(line, "选项"+string(char)) ||
				   strings.Contains(line, "选项 "+string(char)) {
					// 检查这一行是否表示这是正确答案
					if !strings.Contains(line, "不正确") &&
					   !strings.Contains(line, "错误") &&
					   !strings.Contains(line, "不成立") &&
					   !strings.Contains(line, "不符合") &&
					   !strings.Contains(line, "不选") {
						found := false
						for _, existing := range foundAnswers {
							if existing == string(char) {
								found = true
								break
							}
						}
						if !found {
							foundAnswers = append(foundAnswers, string(char))
						}
					}
				}
			}
		}
	}

	// 如果没有找到明确的选项引用，使用简单的字母检测
	if len(foundAnswers) == 0 {
		for _, char := range "ABCD" {
			if strings.Contains(content, string(char)) {
				foundAnswers = append(foundAnswers, string(char))
			}
		}

		// 对于判断题
		if strings.Contains(content, "Y") || strings.Contains(content, "正确") {
			foundAnswers = append(foundAnswers, "Y")
		}
		if strings.Contains(content, "N") || strings.Contains(content, "错误") {
			foundAnswers = append(foundAnswers, "N")
		}
	}

	// 返回找到的答案
	if len(foundAnswers) > 0 {
		return strings.Join(foundAnswers, "")
	}

	return ""
}

// extractAnswerSection 提取答案部分的内容
func extractAnswerSection(content string) string {
	lines := strings.Split(content, "\n")
	var answerLines []string
	inAnswerSection := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		if strings.HasPrefix(line, "答案：") || strings.HasPrefix(line, "答案:") {
			inAnswerSection = true
			// 如果答案在同一行
			if len(line) > 3 {
				answerValue := strings.TrimSpace(line[3:])
				if answerValue != "" {
					answerLines = append(answerLines, answerValue)
				}
			}
			continue
		} else if strings.HasPrefix(line, "解析：") || strings.HasPrefix(line, "解析:") {
			break // 答案部分结束
		}

		if inAnswerSection {
			answerLines = append(answerLines, line)
		}
	}

	return strings.Join(answerLines, "\n")
}

// normalizeOptionContent 标准化选项内容
func normalizeOptionContent(content string) string {
	// 移除多余的空格
	content = strings.TrimSpace(content)
	// 将多个连续空格替换为单个空格
	content = strings.Join(strings.Fields(content), " ")

	// 统一标点符号为半角
	replacements := map[string]string{
		"，": ",",
		"。": ".",
		"；": ";",
		"：": ":",
		"？": "?",
		"！": "!",
		"（": "(",
		"）": ")",
		"【": "[",
		"】": "]",
		"\u201c": "\"",
		"\u201d": "\"",
		"'": "'",
	}

	for fullWidth, halfWidth := range replacements {
		content = strings.ReplaceAll(content, fullWidth, halfWidth)
	}

	return content
}

// cleanUTF8StringDeepSeek 清理UTF-8字符串
func cleanUTF8StringDeepSeek(s string) string {
	s = strings.TrimPrefix(s, "\uFEFF")
	s = strings.ReplaceAll(s, "\ufffd", "")
	if !utf8.ValidString(s) {
		s = strings.ToValidUTF8(s, "")
	}
	return s
}

// getDeepSeekAPIKey 从环境变量获取DeepSeek API Key
func getDeepSeekAPIKey() string {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		// 如果环境变量未设置，使用默认值（仅用于开发环境）
		apiKey = "sk-dd3347aa018244b1a2e19bb364c3c97e"
	}
	return apiKey
}